/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/o/page";
exports.ids = ["app/[locale]/o/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fo%2Fpage&page=%2F%5Blocale%5D%2Fo%2Fpage&appPaths=%2F%5Blocale%5D%2Fo%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fo%2Fpage.tsx&appDir=%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fo%2Fpage&page=%2F%5Blocale%5D%2Fo%2Fpage&appPaths=%2F%5Blocale%5D%2Fo%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fo%2Fpage.tsx&appDir=%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/layout.tsx */ \"(rsc)/./src/app/[locale]/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/o/page.tsx */ \"(rsc)/./src/app/[locale]/o/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: [\n        'o',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/app/[locale]/o/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/app/[locale]/layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/app/[locale]/o/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/o/page\",\n        pathname: \"/[locale]/o\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fo%2Fpage&page=%2F%5Blocale%5D%2Fo%2Fpage&appPaths=%2F%5Blocale%5D%2Fo%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fo%2Fpage.tsx&appDir=%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fsrc%2Fcomponents%2FLanguageSwitcher.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fsrc%2Fcomponents%2FLanguageSwitcher.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/LanguageSwitcher.tsx */ \"(rsc)/./src/components/LanguageSwitcher.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZiMTlyJTJGRGVza3RvcCUyRldvcmslMkZTYWFTJTJGbmluamF0ZW1wbGF0ZXMlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmFwcC1kaXIlMkZsaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyX19lc01vZHVsZSUyMiUyQyUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGaG9tZSUyRmIxOXIlMkZEZXNrdG9wJTJGV29yayUyRlNhYVMlMkZuaW5qYXRlbXBsYXRlcyUyRnNyYyUyRmNvbXBvbmVudHMlMkZMYW5ndWFnZVN3aXRjaGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdOQUF3SztBQUN4SztBQUNBLHNMQUFpSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiX19lc01vZHVsZVwiLFwiZGVmYXVsdFwiXSAqLyBcIi9ob21lL2IxOXIvRGVza3RvcC9Xb3JrL1NhYVMvbmluamF0ZW1wbGF0ZXMvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvYXBwLWRpci9saW5rLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL2hvbWUvYjE5ci9EZXNrdG9wL1dvcmsvU2FhUy9uaW5qYXRlbXBsYXRlcy9zcmMvY29tcG9uZW50cy9MYW5ndWFnZVN3aXRjaGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fsrc%2Fcomponents%2FLanguageSwitcher.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fsrc%2Fcomponents%2FLanguageSwitcher.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fsrc%2Fcomponents%2FLanguageSwitcher.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/LanguageSwitcher.tsx */ \"(ssr)/./src/components/LanguageSwitcher.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZiMTlyJTJGRGVza3RvcCUyRldvcmslMkZTYWFTJTJGbmluamF0ZW1wbGF0ZXMlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmFwcC1kaXIlMkZsaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyX19lc01vZHVsZSUyMiUyQyUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGaG9tZSUyRmIxOXIlMkZEZXNrdG9wJTJGV29yayUyRlNhYVMlMkZuaW5qYXRlbXBsYXRlcyUyRnNyYyUyRmNvbXBvbmVudHMlMkZMYW5ndWFnZVN3aXRjaGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdOQUF3SztBQUN4SztBQUNBLHNMQUFpSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiX19lc01vZHVsZVwiLFwiZGVmYXVsdFwiXSAqLyBcIi9ob21lL2IxOXIvRGVza3RvcC9Xb3JrL1NhYVMvbmluamF0ZW1wbGF0ZXMvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvYXBwLWRpci9saW5rLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL2hvbWUvYjE5ci9EZXNrdG9wL1dvcmsvU2FhUy9uaW5qYXRlbXBsYXRlcy9zcmMvY29tcG9uZW50cy9MYW5ndWFnZVN3aXRjaGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fsrc%2Fcomponents%2FLanguageSwitcher.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZiMTlyJTJGRGVza3RvcCUyRldvcmslMkZTYWFTJTJGbmluamF0ZW1wbGF0ZXMlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZob21lJTJGYjE5ciUyRkRlc2t0b3AlMkZXb3JrJTJGU2FhUyUyRm5pbmphdGVtcGxhdGVzJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGaG9tZSUyRmIxOXIlMkZEZXNrdG9wJTJGV29yayUyRlNhYVMlMkZuaW5qYXRlbXBsYXRlcyUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmVycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZiMTlyJTJGRGVza3RvcCUyRldvcmslMkZTYWFTJTJGbmluamF0ZW1wbGF0ZXMlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZodHRwLWFjY2Vzcy1mYWxsYmFjayUyRmVycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZiMTlyJTJGRGVza3RvcCUyRldvcmslMkZTYWFTJTJGbmluamF0ZW1wbGF0ZXMlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZiMTlyJTJGRGVza3RvcCUyRldvcmslMkZTYWFTJTJGbmluamF0ZW1wbGF0ZXMlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZiMTlyJTJGRGVza3RvcCUyRldvcmslMkZTYWFTJTJGbmluamF0ZW1wbGF0ZXMlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmxpYiUyRm1ldGFkYXRhJTJGbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9PQUF3STtBQUN4STtBQUNBLDBPQUEySTtBQUMzSTtBQUNBLDBPQUEySTtBQUMzSTtBQUNBLG9SQUFnSztBQUNoSztBQUNBLHdPQUEwSTtBQUMxSTtBQUNBLHNRQUF5SjtBQUN6SjtBQUNBLHNPQUF5SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvYjE5ci9EZXNrdG9wL1dvcmsvU2FhUy9uaW5qYXRlbXBsYXRlcy9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2NsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS9iMTlyL0Rlc2t0b3AvV29yay9TYWFTL25pbmphdGVtcGxhdGVzL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvY2xpZW50LXNlZ21lbnQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL2IxOXIvRGVza3RvcC9Xb3JrL1NhYVMvbmluamF0ZW1wbGF0ZXMvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvYjE5ci9EZXNrdG9wL1dvcmsvU2FhUy9uaW5qYXRlbXBsYXRlcy9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2h0dHAtYWNjZXNzLWZhbGxiYWNrL2Vycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS9iMTlyL0Rlc2t0b3AvV29yay9TYWFTL25pbmphdGVtcGxhdGVzL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvYjE5ci9EZXNrdG9wL1dvcmsvU2FhUy9uaW5qYXRlbXBsYXRlcy9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL2IxOXIvRGVza3RvcC9Xb3JrL1NhYVMvbmluamF0ZW1wbGF0ZXMvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9saWIvbWV0YWRhdGEvbWV0YWRhdGEtYm91bmRhcnkuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/components/LanguageSwitcher.tsx":
/*!*********************************************!*\
  !*** ./src/components/LanguageSwitcher.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LanguageSwitcher)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _lib_i18n_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/i18n/config */ \"(ssr)/./src/lib/i18n/config.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction LanguageSwitcher({ currentLocale }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const switchLanguage = (newLocale)=>{\n        // Set cookie\n        document.cookie = `locale=${newLocale}; Path=/; SameSite=Lax; Max-Age=31536000`;\n        // Get the pathname without the current locale\n        const pathWithoutLocale = pathname.replace(`/${currentLocale}`, \"\") || \"/\";\n        // Navigate to the new locale\n        router.push(`/${newLocale}${pathWithoutLocale}`);\n        setIsOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center gap-2 px-3 py-2 bg-background border border-border rounded-lg shadow-sm hover:bg-hover transition-all duration-200\",\n                \"aria-label\": \"Switch language\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-4 h-4 text-text-light\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/LanguageSwitcher.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium text-text\",\n                        children: [\n                            _lib_i18n_config__WEBPACK_IMPORTED_MODULE_3__.localeFlags[currentLocale],\n                            \" \",\n                            _lib_i18n_config__WEBPACK_IMPORTED_MODULE_3__.localeNames[currentLocale]\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/LanguageSwitcher.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: `w-4 h-4 text-text-light transition-transform duration-200 ${isOpen ? \"rotate-180\" : \"\"}`\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/LanguageSwitcher.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/LanguageSwitcher.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-10\",\n                        onClick: ()=>setIsOpen(false)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/LanguageSwitcher.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-full mt-2 right-0 z-20 bg-background border border-border rounded-lg shadow-lg min-w-[180px]\",\n                        children: _lib_i18n_config__WEBPACK_IMPORTED_MODULE_3__.i18n.locales.map((locale)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>switchLanguage(locale),\n                                className: `w-full flex items-center gap-3 px-4 py-3 text-sm hover:bg-hover transition-colors duration-200 first:rounded-t-lg last:rounded-b-lg ${locale === currentLocale ? \"bg-primary/10 text-primary\" : \"text-text\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: _lib_i18n_config__WEBPACK_IMPORTED_MODULE_3__.localeFlags[locale]\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/LanguageSwitcher.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: _lib_i18n_config__WEBPACK_IMPORTED_MODULE_3__.localeNames[locale]\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/LanguageSwitcher.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 17\n                                    }, this),\n                                    locale === currentLocale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-auto text-primary\",\n                                        children: \"✓\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/LanguageSwitcher.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, locale, true, {\n                                fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/LanguageSwitcher.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/LanguageSwitcher.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/LanguageSwitcher.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LanguageSwitcher.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/i18n/config.ts":
/*!********************************!*\
  !*** ./src/lib/i18n/config.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   i18n: () => (/* binding */ i18n),\n/* harmony export */   localeFlags: () => (/* binding */ localeFlags),\n/* harmony export */   localeNames: () => (/* binding */ localeNames)\n/* harmony export */ });\nconst i18n = {\n    defaultLocale: \"en\",\n    locales: [\n        \"en\",\n        \"ar\"\n    ]\n};\nconst localeNames = {\n    en: \"English\",\n    ar: \"العربية\"\n};\nconst localeFlags = {\n    en: \"🇺🇸\",\n    ar: \"🇸🇦\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2kxOG4vY29uZmlnLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFPLE1BQU1BLE9BQU87SUFDbEJDLGVBQWU7SUFDZkMsU0FBUztRQUFDO1FBQU07S0FBSztBQUN2QixFQUFXO0FBSUosTUFBTUMsY0FBc0M7SUFDakRDLElBQUk7SUFDSkMsSUFBSTtBQUNOLEVBQUU7QUFFSyxNQUFNQyxjQUFzQztJQUNqREYsSUFBSTtJQUNKQyxJQUFJO0FBQ04sRUFBRSIsInNvdXJjZXMiOlsiL2hvbWUvYjE5ci9EZXNrdG9wL1dvcmsvU2FhUy9uaW5qYXRlbXBsYXRlcy9zcmMvbGliL2kxOG4vY29uZmlnLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBpMThuID0ge1xuICBkZWZhdWx0TG9jYWxlOiBcImVuXCIsXG4gIGxvY2FsZXM6IFtcImVuXCIsIFwiYXJcIl0sXG59IGFzIGNvbnN0O1xuXG5leHBvcnQgdHlwZSBMb2NhbGUgPSAodHlwZW9mIGkxOG4pW1wibG9jYWxlc1wiXVtudW1iZXJdO1xuXG5leHBvcnQgY29uc3QgbG9jYWxlTmFtZXM6IFJlY29yZDxMb2NhbGUsIHN0cmluZz4gPSB7XG4gIGVuOiBcIkVuZ2xpc2hcIixcbiAgYXI6IFwi2KfZhNi52LHYqNmK2KlcIixcbn07XG5cbmV4cG9ydCBjb25zdCBsb2NhbGVGbGFnczogUmVjb3JkPExvY2FsZSwgc3RyaW5nPiA9IHtcbiAgZW46IFwi8J+HuvCfh7hcIixcbiAgYXI6IFwi8J+HuPCfh6ZcIixcbn07XG4iXSwibmFtZXMiOlsiaTE4biIsImRlZmF1bHRMb2NhbGUiLCJsb2NhbGVzIiwibG9jYWxlTmFtZXMiLCJlbiIsImFyIiwibG9jYWxlRmxhZ3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/i18n/config.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"dfc530b8b356\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvaG9tZS9iMTlyL0Rlc2t0b3AvV29yay9TYWFTL25pbmphdGVtcGxhdGVzL3NyYy9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkZmM1MzBiOGIzNTZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/layout.tsx":
/*!*************************************!*\
  !*** ./src/app/[locale]/layout.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocaleLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Navigation */ \"(rsc)/./src/components/Navigation.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./src/components/Footer.tsx\");\n/* harmony import */ var _lib_i18n_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/i18n/utils */ \"(rsc)/./src/lib/i18n/utils.ts\");\n\n\n\n\nasync function LocaleLayout({ children, params }) {\n    const { locale } = await params;\n    const t = await (0,_lib_i18n_utils__WEBPACK_IMPORTED_MODULE_3__.getDictionary)(locale);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        dir: locale === \"ar\" ? \"rtl\" : \"ltr\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans\",\n            suppressHydrationWarning: true,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    locale: locale,\n                    t: t\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/app/[locale]/layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"min-h-screen\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/app/[locale]/layout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    locale: locale,\n                    t: t\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/app/[locale]/layout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/app/[locale]/layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/app/[locale]/layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/o/page.tsx":
/*!*************************************!*\
  !*** ./src/app/[locale]/o/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: \"WELCOME\"\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/app/[locale]/o/page.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL1tsb2NhbGVdL28vcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTBCO0FBRVgsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNDO2tCQUFJOzs7Ozs7QUFDZCIsInNvdXJjZXMiOlsiL2hvbWUvYjE5ci9EZXNrdG9wL1dvcmsvU2FhUy9uaW5qYXRlbXBsYXRlcy9zcmMvYXBwL1tsb2NhbGVdL28vcGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBwYWdlKCkge1xuICByZXR1cm4gPGRpdj5XRUxDT01FPC9kaXY+O1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwicGFnZSIsImRpdiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/o/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\nconst metadata = {\n    title: \"DigitalMarket - Digital Marketplace\",\n    description: \"A comprehensive digital marketplace for templates, plugins, and digital products\"\n};\nfunction RootLayout({ children }) {\n    return children;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQ3VCO0FBRWhCLE1BQU1BLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQ0U7QUFDSixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MsT0FBT0E7QUFDVCIsInNvdXJjZXMiOlsiL2hvbWUvYjE5ci9EZXNrdG9wL1dvcmsvU2FhUy9uaW5qYXRlbXBsYXRlcy9zcmMvYXBwL2xheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkRpZ2l0YWxNYXJrZXQgLSBEaWdpdGFsIE1hcmtldHBsYWNlXCIsXG4gIGRlc2NyaXB0aW9uOlxuICAgIFwiQSBjb21wcmVoZW5zaXZlIGRpZ2l0YWwgbWFya2V0cGxhY2UgZm9yIHRlbXBsYXRlcywgcGx1Z2lucywgYW5kIGRpZ2l0YWwgcHJvZHVjdHNcIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0pIHtcbiAgcmV0dXJuIGNoaWxkcmVuO1xufVxuIl0sIm5hbWVzIjpbIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Footer({ locale, t }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-background-dark text-text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-text-muted text-sm\",\n                    children: t.footer.text\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/Footer.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/Footer.tsx\",\n                lineNumber: 12,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/Footer.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/Footer.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9Gb290ZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFPZSxTQUFTQSxPQUFPLEVBQUVDLE1BQU0sRUFBRUMsQ0FBQyxFQUFlO0lBQ3ZELHFCQUNFLDhEQUFDQztRQUFPQyxXQUFVO2tCQUNoQiw0RUFBQ0M7WUFBSUQsV0FBVTtzQkFDYiw0RUFBQ0M7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNFO29CQUFFRixXQUFVOzhCQUEyQkYsRUFBRUMsTUFBTSxDQUFDSSxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLL0QiLCJzb3VyY2VzIjpbIi9ob21lL2IxOXIvRGVza3RvcC9Xb3JrL1NhYVMvbmluamF0ZW1wbGF0ZXMvc3JjL2NvbXBvbmVudHMvRm9vdGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIExvY2FsZSB9IGZyb20gXCJAL2xpYi9pMThuL2NvbmZpZ1wiO1xuXG5pbnRlcmZhY2UgRm9vdGVyUHJvcHMge1xuICBsb2NhbGU6IExvY2FsZTtcbiAgdDogYW55O1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBGb290ZXIoeyBsb2NhbGUsIHQgfTogRm9vdGVyUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8Zm9vdGVyIGNsYXNzTmFtZT1cImJnLWJhY2tncm91bmQtZGFyayB0ZXh0LXRleHQtd2hpdGVcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS04XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXRleHQtbXV0ZWQgdGV4dC1zbVwiPnt0LmZvb3Rlci50ZXh0fTwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Zvb3Rlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJGb290ZXIiLCJsb2NhbGUiLCJ0IiwiZm9vdGVyIiwiY2xhc3NOYW1lIiwiZGl2IiwicCIsInRleHQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/LanguageSwitcher.tsx":
/*!*********************************************!*\
  !*** ./src/components/LanguageSwitcher.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/LanguageSwitcher.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/LanguageSwitcher.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _LanguageSwitcher__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./LanguageSwitcher */ \"(rsc)/./src/components/LanguageSwitcher.tsx\");\n\n\n\nfunction Navigation({ locale, t }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-background shadow-sm border-b border-border\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: `/${locale}`,\n                        className: \"text-2xl font-bold text-primary hover:text-primary-hover transition-colors\",\n                        children: t.nav.title\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/Navigation.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        currentLocale: locale\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/Navigation.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/Navigation.tsx\",\n                lineNumber: 14,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/Navigation.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/Work/SaaS/ninjatemplates/src/components/Navigation.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9OYXZpZ2F0aW9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTZCO0FBQ3FCO0FBUW5DLFNBQVNFLFdBQVcsRUFBRUMsTUFBTSxFQUFFQyxDQUFDLEVBQW1CO0lBQy9ELHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDQztZQUFJRCxXQUFVO3NCQUNiLDRFQUFDQztnQkFBSUQsV0FBVTs7a0NBRWIsOERBQUNOLGtEQUFJQTt3QkFDSFEsTUFBTSxDQUFDLENBQUMsRUFBRUwsUUFBUTt3QkFDbEJHLFdBQVU7a0NBRVRGLEVBQUVDLEdBQUcsQ0FBQ0ksS0FBSzs7Ozs7O2tDQUlkLDhEQUFDUix5REFBZ0JBO3dCQUFDUyxlQUFlUDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUszQyIsInNvdXJjZXMiOlsiL2hvbWUvYjE5ci9EZXNrdG9wL1dvcmsvU2FhUy9uaW5qYXRlbXBsYXRlcy9zcmMvY29tcG9uZW50cy9OYXZpZ2F0aW9uLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTGluayBmcm9tIFwibmV4dC9saW5rXCI7XG5pbXBvcnQgTGFuZ3VhZ2VTd2l0Y2hlciBmcm9tIFwiLi9MYW5ndWFnZVN3aXRjaGVyXCI7XG5pbXBvcnQgeyB0eXBlIExvY2FsZSB9IGZyb20gXCJAL2xpYi9pMThuL2NvbmZpZ1wiO1xuXG5pbnRlcmZhY2UgTmF2aWdhdGlvblByb3BzIHtcbiAgbG9jYWxlOiBMb2NhbGU7XG4gIHQ6IGFueTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTmF2aWdhdGlvbih7IGxvY2FsZSwgdCB9OiBOYXZpZ2F0aW9uUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8bmF2IGNsYXNzTmFtZT1cImJnLWJhY2tncm91bmQgc2hhZG93LXNtIGJvcmRlci1iIGJvcmRlci1ib3JkZXJcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBoLTE2XCI+XG4gICAgICAgICAgey8qIExvZ28gKi99XG4gICAgICAgICAgPExpbmtcbiAgICAgICAgICAgIGhyZWY9e2AvJHtsb2NhbGV9YH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXByaW1hcnkgaG92ZXI6dGV4dC1wcmltYXJ5LWhvdmVyIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICB7dC5uYXYudGl0bGV9XG4gICAgICAgICAgPC9MaW5rPlxuXG4gICAgICAgICAgey8qIExhbmd1YWdlIFN3aXRjaGVyICovfVxuICAgICAgICAgIDxMYW5ndWFnZVN3aXRjaGVyIGN1cnJlbnRMb2NhbGU9e2xvY2FsZX0gLz5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L25hdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJMaW5rIiwiTGFuZ3VhZ2VTd2l0Y2hlciIsIk5hdmlnYXRpb24iLCJsb2NhbGUiLCJ0IiwibmF2IiwiY2xhc3NOYW1lIiwiZGl2IiwiaHJlZiIsInRpdGxlIiwiY3VycmVudExvY2FsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/components/Navigation.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/i18n/utils.ts":
/*!*******************************!*\
  !*** ./src/lib/i18n/utils.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDictionary: () => (/* binding */ getDictionary),\n/* harmony export */   getLocaleFromCookie: () => (/* binding */ getLocaleFromCookie),\n/* harmony export */   setLocaleCookie: () => (/* binding */ setLocaleCookie)\n/* harmony export */ });\nconst dictionaries = {\n    en: ()=>__webpack_require__.e(/*! import() */ \"_rsc_src_dictionaries_en_json\").then(__webpack_require__.t.bind(__webpack_require__, /*! @/dictionaries/en.json */ \"(rsc)/./src/dictionaries/en.json\", 19)).then((module)=>module.default),\n    ar: ()=>__webpack_require__.e(/*! import() */ \"_rsc_src_dictionaries_ar_json\").then(__webpack_require__.t.bind(__webpack_require__, /*! @/dictionaries/ar.json */ \"(rsc)/./src/dictionaries/ar.json\", 19)).then((module)=>module.default)\n};\nconst getDictionary = async (locale)=>{\n    return dictionaries[locale]?.() ?? dictionaries.en();\n};\nfunction setLocaleCookie(locale) {\n    return `locale=${locale}; Path=/; HttpOnly; SameSite=Lax; Max-Age=31536000`;\n}\nfunction getLocaleFromCookie(cookieHeader) {\n    if (!cookieHeader) return null;\n    const cookies = cookieHeader.split(\";\").map((cookie)=>cookie.trim());\n    const localeCookie = cookies.find((cookie)=>cookie.startsWith(\"locale=\"));\n    if (localeCookie) {\n        const locale = localeCookie.split(\"=\")[1];\n        return locale;\n    }\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/i18n/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fo%2Fpage&page=%2F%5Blocale%5D%2Fo%2Fpage&appPaths=%2F%5Blocale%5D%2Fo%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fo%2Fpage.tsx&appDir=%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fb19r%2FDesktop%2FWork%2FSaaS%2Fninjatemplates&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();